<script setup>
import { ref } from 'vue'

// 响应式数据
const isExpanded = ref(false)

// 缴费说明内容
const noticeItems = [
  {
    icon: 'i-carbon-calendar',
    title: '租金缴纳',
    content: '租金按季度缴纳，每期需在到期前 10 天内支付，确保居住权益不受影响。'
  },
  {
    icon: 'i-carbon-flash',
    title: '水电费用',
    content: '水电费每月 5 日生成账单，可随时缴纳。建议及时关注用量，节约能源。'
  },
  {
    icon: 'i-carbon-warning',
    title: '逾期处理',
    content: '逾期未缴按日收取 0.05% 滞纳金，逾期 30 天将影响信用记录，请务必按时缴费。'
  },
  {
    icon: 'i-carbon-document',
    title: '收据获取',
    content: '缴费成功后可在"查看收据"中获取电子收据，如需纸质收据请联系前台办理。'
  }
]

// 方法
function toggleExpanded() {
  isExpanded.value = !isExpanded.value
  uni.vibrateShort?.()
}
</script>

<template>
  <view class="payment-notice">
    <wd-card class="notice-card">
      <template #title>
        <view class="notice-header" @click="toggleExpanded">
          <view class="header-left">
            <view class="i-carbon-information text-primary-500 text-xl"></view>
            <view class="header-text">
              <view class="title">缴费说明</view>
              <view class="subtitle">重要提醒和注意事项</view>
            </view>
          </view>
          <view class="expand-icon" :class="{ 'expanded': isExpanded }">
            <view class="i-carbon-chevron-down text-gray-400"></view>
          </view>
        </view>
      </template>

      <view class="notice-content" :class="{ 'expanded': isExpanded }">
        <view class="notice-list">
          <view 
            v-for="(item, index) in noticeItems" 
            :key="index"
            class="notice-item"
          >
            <view class="item-icon">
              <view :class="item.icon" class="icon"></view>
            </view>
            <view class="item-content">
              <view class="item-title">{{ item.title }}</view>
              <view class="item-text">{{ item.content }}</view>
            </view>
          </view>
        </view>
        
        <!-- 联系方式 -->
        <view class="contact-info">
          <view class="contact-title">
            <view class="i-carbon-phone text-primary-500 mr-2"></view>
            需要帮助？
          </view>
          <view class="contact-content">
            <view class="contact-item">
              <text class="contact-label">客服热线：</text>
              <text class="contact-value phone" @click="handleCall('************')">************</text>
            </view>
            <view class="contact-item">
              <text class="contact-label">服务时间：</text>
              <text class="contact-value">周一至周日 9:00-21:00</text>
            </view>
          </view>
        </view>
      </view>
    </wd-card>
  </view>
</template>

<script>
export default {
  methods: {
    handleCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        success: () => {
          console.log('拨打电话成功')
        },
        fail: (err) => {
          console.error('拨打电话失败', err)
          uni.showToast({
            title: '拨打失败',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-notice {
  margin: 16px;
  margin-top: 0;
}

.notice-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  
  :deep(.wd-card__header) {
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    cursor: pointer;
  }
  
  :deep(.wd-card__body) {
    padding: 0;
    background: white;
  }
}

.notice-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-text {
  .title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.2;
  }
  
  .subtitle {
    font-size: 14px;
    color: #6b7280;
    margin-top: 2px;
  }
}

.expand-icon {
  transition: transform 0.3s ease;
  
  &.expanded {
    transform: rotate(180deg);
  }
}

.notice-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  
  &.expanded {
    max-height: 1000px;
  }
}

.notice-list {
  padding: 20px;
  padding-bottom: 16px;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.item-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .icon {
    font-size: 16px;
    color: #0284c7;
  }
}

.item-content {
  flex: 1;
  
  .item-title {
    font-size: 15px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
    line-height: 1.3;
  }
  
  .item-text {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
  }
}

.contact-info {
  background: #f8fafc;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
}

.contact-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.contact-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  
  .contact-label {
    color: #6b7280;
    min-width: 80px;
  }
  
  .contact-value {
    color: #1f2937;
    font-weight: 500;
    
    &.phone {
      color: #0284c7;
      text-decoration: underline;
      cursor: pointer;
      
      &:hover {
        color: #0369a1;
      }
      
      &:active {
        color: #075985;
      }
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .notice-card {
    :deep(.wd-card__header) {
      padding: 16px;
    }
  }
  
  .notice-list {
    padding: 16px;
    padding-bottom: 12px;
  }
  
  .contact-info {
    padding: 16px;
  }
  
  .notice-item {
    margin-bottom: 16px;
  }
  
  .item-icon {
    width: 28px;
    height: 28px;
    
    .icon {
      font-size: 14px;
    }
  }
  
  .item-content {
    .item-title {
      font-size: 14px;
    }
    
    .item-text {
      font-size: 13px;
    }
  }
}
</style>
