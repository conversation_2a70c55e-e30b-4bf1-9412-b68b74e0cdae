<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  categories: {
    type: Array,
    default: () => [
      { id: 'all', name: '全部费用', count: 8 },
      { id: 'rent', name: '租金', count: 4 },
      { id: 'property', name: '物业费', count: 2 },
      { id: 'utilities', name: '水电费', count: 1 },
      { id: 'deposit', name: '押金', count: 1 }
    ]
  },
  activeCategory: {
    type: String,
    default: 'all'
  }
})

// Emits
const emit = defineEmits(['categoryChange'])

// 响应式数据
const currentTab = ref(0)

// 计算属性
const tabIndex = computed({
  get() {
    return props.categories.findIndex(cat => cat.id === props.activeCategory)
  },
  set(index) {
    const category = props.categories[index]
    if (category) {
      emit('categoryChange', category.id)
    }
  }
})

// 方法
function handleTabClick(event) {
  const { index } = event.detail
  const category = props.categories[index]
  if (category) {
    uni.vibrateShort?.()
    emit('categoryChange', category.id)
  }
}
</script>

<template>
  <view class="category-tabs-container">
    <wd-tabs 
      v-model="tabIndex" 
      @click="handleTabClick"
      class="payment-tabs"
      swipeable
    >
      <wd-tab 
        v-for="(category, index) in categories" 
        :key="category.id"
        :title="category.name"
      >
        <view class="tab-content">
          <!-- 这里的内容由父组件控制，tab只负责切换 -->
          <slot :category="category" :index="index"></slot>
        </view>
      </wd-tab>
    </wd-tabs>
    
    <!-- 自定义标签头部，显示数量 -->
    <view class="custom-tab-header">
      <view 
        v-for="(category, index) in categories" 
        :key="category.id"
        class="tab-item"
        :class="{ 'active': index === tabIndex }"
        @click="() => { tabIndex = index }"
      >
        <view class="tab-name">{{ category.name }}</view>
        <view v-if="category.count > 0" class="tab-count">{{ category.count }}</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.category-tabs-container {
  background: white;
  position: relative;
  
  // 隐藏默认的tabs头部，使用自定义的
  :deep(.wd-tabs__nav) {
    display: none;
  }
  
  :deep(.wd-tabs__content) {
    padding-top: 0;
  }
}

.custom-tab-header {
  display: flex;
  background: white;
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

.tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: fit-content;
  
  &:last-child {
    margin-right: 0;
  }
  
  &.active {
    background: linear-gradient(135deg, rgba(var(--primary-color-500), 1) 0%, rgba(var(--primary-color-600), 1) 100%);
    border-color: rgba(var(--primary-color-500), 1);
    color: white;
    box-shadow: 0 2px 8px rgba(var(--primary-color-500), 0.3);
    transform: translateY(-1px);
    
    .tab-count {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }
  }
  
  &:not(.active):hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
  }
  
  &:not(.active):active {
    transform: translateY(0);
  }
}

.tab-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  
  .active & {
    color: white;
  }
}

.tab-count {
  background: #e5e7eb;
  color: #6b7280;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 6px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

.tab-content {
  min-height: 200px;
  
  // 添加一些基础样式
  :deep(.content-wrapper) {
    padding: 16px;
  }
}

// 响应式设计
@media (max-width: 375px) {
  .custom-tab-header {
    padding: 12px;
  }
  
  .tab-item {
    padding: 6px 12px;
    margin-right: 6px;
  }
  
  .tab-name {
    font-size: 13px;
  }
  
  .tab-count {
    font-size: 11px;
    padding: 1px 4px;
    margin-left: 4px;
  }
}
</style>
