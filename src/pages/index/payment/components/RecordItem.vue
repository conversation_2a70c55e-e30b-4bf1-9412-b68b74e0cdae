<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  record: {
    type: Object,
    required: true,
  },
})

// Emits
const emit = defineEmits(['viewReceipt', 'payNow'])

// 计算属性
const statusConfig = computed(() => {
  const configs = {
    pending: {
      text: '待缴费',
      class: 'status-pending',
      color: '#f59e0b',
      bgColor: '#fef3c7',
      icon: 'i-carbon-time',
    },
    paid: {
      text: '已缴费',
      class: 'status-paid',
      color: '#10b981',
      bgColor: '#d1fae5',
      icon: 'i-carbon-checkmark-filled',
    },
    overdue: {
      text: '已逾期',
      class: 'status-overdue',
      color: '#ef4444',
      bgColor: '#fee2e2',
      icon: 'i-carbon-warning',
    },
  }
  return configs[props.record.status] || configs.pending
})

const formattedAmount = computed(() => {
  return `¥${props.record.amount.toFixed(2)}`
})

const categoryName = computed(() => {
  const categoryMap = {
    rent: '租金',
    property: '物业费',
    utilities: '水电费',
    deposit: '押金',
  }
  return categoryMap[props.record.category] || '其他'
})

const paymentMethodText = computed(() => {
  const methodMap = {
    wechat: '微信支付',
    alipay: '支付宝',
    bank: '银行转账',
    cash: '现金支付',
  }
  return methodMap[props.record.paymentMethod] || '未知'
})

// 方法
function handleViewReceipt() {
  if (props.record.status === 'paid') {
    emit('viewReceipt', props.record)
  }
}

function handlePayNow() {
  if (props.record.status === 'pending') {
    emit('payNow', props.record)
  }
}

function formatDate(dateString) {
  if (!dateString)
    return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

function formatDateTime(dateString) {
  if (!dateString)
    return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}
</script>

<template>
  <wd-card class="record-item" :class="statusConfig.class">
    <view class="record-content">
      <!-- 头部信息 -->
      <view class="record-header">
        <view class="record-title">
          <view class="title-main">
            {{ record.title }}
          </view>
          <view class="title-sub">
            {{ categoryName }} · {{ record.billNumber }}
          </view>
        </view>
        <view class="record-amount">
          {{ formattedAmount }}
        </view>
      </view>

      <!-- 状态和时间信息 -->
      <view class="record-meta">
        <view class="status-info">
          <view
            class="status-badge"
            :style="{
              color: statusConfig.color,
              backgroundColor: statusConfig.bgColor,
            }"
          >
            <view :class="statusConfig.icon" class="status-icon"></view>
            {{ statusConfig.text }}
          </view>
        </view>

        <view class="time-info">
          <view v-if="record.status === 'pending'" class="due-date">
            截止：{{ formatDate(record.dueDate) }}
          </view>
          <view v-else-if="record.paidTime" class="paid-time">
            {{ formatDateTime(record.paidTime) }}
          </view>
        </view>
      </view>

      <!-- 支付信息（已支付记录） -->
      <view v-if="record.status === 'paid' && record.paymentMethod" class="payment-info">
        <view class="payment-method">
          <view class="i-carbon-wallet text-gray-400 mr-1"></view>
          {{ paymentMethodText }}
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="record-actions">
        <wd-button
          v-if="record.status === 'paid'"
          type="info"
          size="small"
          plain
          class="action-btn"
          @click="handleViewReceipt"
        >
          <view class="i-carbon-document mr-1"></view>
          查看收据
        </wd-button>

        <wd-button
          v-else-if="record.status === 'pending'"
          type="primary"
          size="small"
          class="action-btn pay-btn"
          @click="handlePayNow"
        >
          <view class="i-carbon-payment mr-1"></view>
          立即缴费
        </wd-button>

        <wd-button
          v-else-if="record.status === 'overdue'"
          type="error"
          size="small"
          class="action-btn pay-btn"
          @click="handlePayNow"
        >
          <view class="i-carbon-warning mr-1"></view>
          逾期缴费
        </wd-button>
      </view>
    </view>
  </wd-card>
</template>

<style lang="scss" scoped>
.record-item {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  &.status-pending {
    border-left: 4px solid #f59e0b;
  }

  &.status-paid {
    border-left: 4px solid #10b981;
  }

  &.status-overdue {
    border-left: 4px solid #ef4444;
  }

  :deep(.wd-card__body) {
    padding: 16px;
  }
}

.record-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.record-title {
  flex: 1;

  .title-main {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.3;
    margin-bottom: 4px;
  }

  .title-sub {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.2;
  }
}

.record-amount {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  text-align: right;
  white-space: nowrap;
}

.record-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  .status-icon {
    font-size: 12px;
  }
}

.time-info {
  font-size: 12px;
  color: #6b7280;
  text-align: right;

  .due-date {
    color: #f59e0b;
    font-weight: 500;
  }
}

.payment-info {
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;

  .payment-method {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #6b7280;
  }
}

.record-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;

  .action-btn {
    border-radius: 16px;

    :deep(.wd-button__text) {
      display: flex;
      align-items: center;
      font-size: 13px;
    }

    &.pay-btn {
      box-shadow: 0 2px 4px rgba(var(--primary-color-500), 0.2);
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .record-header {
    flex-direction: column;
    gap: 8px;
  }

  .record-amount {
    align-self: flex-end;
    font-size: 16px;
  }

  .record-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .time-info {
    align-self: flex-end;
  }
}
</style>
