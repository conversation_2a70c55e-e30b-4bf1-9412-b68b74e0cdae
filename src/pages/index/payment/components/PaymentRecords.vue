<script setup>
import { computed, ref } from 'vue'
import RecordItem from './RecordItem.vue'

// Props
const props = defineProps({
  records: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  hasMore: {
    type: Boolean,
    default: true,
  },
  activeCategory: {
    type: String,
    default: 'all',
  },
})

// Emits
const emit = defineEmits(['loadMore', 'viewReceipt', 'payNow'])

// 响应式数据
const loadingMore = ref(false)

// 计算属性
const filteredRecords = computed(() => {
  if (props.activeCategory === 'all') {
    return props.records
  }
  return props.records.filter(record => record.category === props.activeCategory)
})

const pendingRecords = computed(() => {
  return filteredRecords.value.filter(record => record.status === 'pending')
})

const paidRecords = computed(() => {
  return filteredRecords.value.filter(record => record.status === 'paid')
})

const hasRecords = computed(() => {
  return filteredRecords.value.length > 0
})

// 方法
async function handleLoadMore() {
  if (loadingMore.value || !props.hasMore)
    return

  try {
    loadingMore.value = true
    emit('loadMore')
  }
  finally {
    loadingMore.value = false
  }
}

function handleViewReceipt(record) {
  emit('viewReceipt', record)
}

function handlePayNow(record) {
  emit('payNow', record)
}
</script>

<template>
  <view class="payment-records">
    <!-- 加载状态 -->
    <view v-if="loading && !hasRecords" class="loading-container">
      <wd-loading />
      <text class="loading-text">
        加载中...
      </text>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!hasRecords" class="empty-container">
      <view class="empty-icon">
        <view class="i-carbon-document text-6xl text-gray-300"></view>
      </view>
      <view class="empty-text">
        暂无缴费记录
      </view>
      <view class="empty-desc">
        您在该分类下还没有任何缴费记录
      </view>
    </view>

    <!-- 记录列表 -->
    <view v-else class="records-container">
      <!-- 待缴费用 -->
      <view v-if="pendingRecords.length > 0" class="section">
        <view class="section-header">
          <view class="section-title">
            <view class="i-carbon-time text-warning mr-2"></view>
            待缴费用
          </view>
          <view class="section-count">
            {{ pendingRecords.length }} 笔
          </view>
        </view>
        <view class="records-list">
          <RecordItem
            v-for="record in pendingRecords"
            :key="record.id"
            :record="record"
            @viewReceipt="handleViewReceipt"
            @payNow="handlePayNow"
          />
        </view>
      </view>

      <!-- 已缴费用 -->
      <view v-if="paidRecords.length > 0" class="section">
        <view class="section-header">
          <view class="section-title">
            <view class="i-carbon-checkmark-filled text-success mr-2"></view>
            已缴费用
          </view>
          <view class="section-count">
            {{ paidRecords.length }} 笔
          </view>
        </view>
        <view class="records-list">
          <RecordItem
            v-for="record in paidRecords"
            :key="record.id"
            :record="record"
            @viewReceipt="handleViewReceipt"
            @payNow="handlePayNow"
          />
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more-container">
        <wd-button
          v-if="!loadingMore"
          type="info"
          plain
          size="small"
          class="load-more-btn"
          @click="handleLoadMore"
        >
          <view class="i-carbon-chevron-down mr-1"></view>
          加载更多
        </wd-button>
        <view v-else class="loading-more">
          <wd-loading size="16px" />
          <text class="loading-text">
            加载中...
          </text>
        </view>
      </view>

      <!-- 没有更多 -->
      <view v-else-if="filteredRecords.length > 0" class="no-more">
        <view class="no-more-line"></view>
        <text class="no-more-text">
          没有更多记录了
        </text>
        <view class="no-more-line"></view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.payment-records {
  background: #f8fafc;
  min-height: 400px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .loading-text {
    margin-top: 12px;
    font-size: 14px;
    color: #6b7280;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .empty-icon {
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    color: #374151;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .empty-desc {
    font-size: 14px;
    color: #6b7280;
    text-align: center;
    line-height: 1.5;
  }
}

.records-container {
  padding: 16px;
}

.section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 0 4px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.section-count {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 12px;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.load-more-container {
  display: flex;
  justify-content: center;
  padding: 20px;

  .load-more-btn {
    border-radius: 20px;

    :deep(.wd-button__text) {
      display: flex;
      align-items: center;
    }
  }

  .loading-more {
    display: flex;
    align-items: center;
    gap: 8px;

    .loading-text {
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 12px;

  .no-more-line {
    flex: 1;
    height: 1px;
    background: #e5e7eb;
    max-width: 60px;
  }

  .no-more-text {
    font-size: 12px;
    color: #9ca3af;
    white-space: nowrap;
  }
}
</style>
