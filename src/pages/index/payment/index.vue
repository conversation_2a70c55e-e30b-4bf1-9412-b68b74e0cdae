<script setup>
import { showModal, showToast } from '@uni-helper/uni-promises'
import { computed, onMounted, ref } from 'vue'
import PaymentCategoryTabs from './components/PaymentCategoryTabs.vue'
import PaymentNotice from './components/PaymentNotice.vue'
import PaymentRecords from './components/PaymentRecords.vue'
import PendingPaymentCard from './components/PendingPaymentCard.vue'

// 页面标题
uni.setNavigationBarTitle({
  title: '在线缴费',
})

// 响应式数据
const loading = ref(false)
const activeCategory = ref('all')
const hasMore = ref(true)

// 待缴费用数据
const pendingPayment = ref({
  amount: 2650.00,
  description: '2024年12月租金 + 物业费',
  dueDate: '2024-12-10',
  isOverdue: false,
})

// 分类数据
const categories = ref([
  { id: 'all', name: '全部费用', count: 8 },
  { id: 'rent', name: '租金', count: 4 },
  { id: 'property', name: '物业费', count: 2 },
  { id: 'utilities', name: '水电费', count: 1 },
  { id: 'deposit', name: '押金', count: 1 },
])

// 缴费记录数据
const paymentRecords = ref([
  {
    id: 'PAY20241201001',
    title: '2024年12月租金',
    amount: 2650.00,
    category: 'rent',
    status: 'pending',
    billNumber: 'PAY20241201001',
    dueDate: '2024-12-10',
    createTime: '2024-12-01 09:00:00',
  },
  {
    id: 'PAY20241101001',
    title: '2024年11月水电费',
    amount: 135.80,
    category: 'utilities',
    status: 'paid',
    billNumber: 'PAY20241101001',
    paymentMethod: 'wechat',
    paidTime: '2024-11-05 14:30:00',
    createTime: '2024-11-01 09:00:00',
  },
  {
    id: 'PAY20240901001',
    title: '2024年9-11月租金',
    amount: 7950.00,
    category: 'rent',
    status: 'paid',
    billNumber: 'PAY20240901001',
    paymentMethod: 'wechat',
    paidTime: '2024-09-01 16:45:00',
    createTime: '2024-08-25 09:00:00',
  },
  {
    id: 'PAY20240801001',
    title: '租房押金',
    amount: 2500.00,
    category: 'deposit',
    status: 'paid',
    billNumber: 'PAY20240801001',
    paymentMethod: 'wechat',
    paidTime: '2024-08-01 10:20:00',
    createTime: '2024-08-01 09:00:00',
  },
])

// 计算属性
const filteredRecords = computed(() => {
  if (activeCategory.value === 'all') {
    return paymentRecords.value
  }
  return paymentRecords.value.filter(record => record.category === activeCategory.value)
})

// 生命周期
onMounted(() => {
  loadPaymentData()
})

// 方法
async function loadPaymentData() {
  try {
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里可以调用实际的API获取数据
    // const response = await api.getPaymentData()
    // paymentRecords.value = response.data
  }
  catch (error) {
    console.error('加载缴费数据失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  }
  finally {
    loading.value = false
  }
}

// 分类切换
function handleCategoryChange(categoryId) {
  activeCategory.value = categoryId
  uni.vibrateShort?.()
}

// 立即缴费
async function handlePayNow(payment) {
  try {
    const result = await showModal({
      title: '确认缴费',
      content: `确定要缴纳 ${payment.amount ? `¥${payment.amount.toFixed(2)}` : pendingPayment.value.amount.toFixed(2)} 的费用吗？`,
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消',
    })

    if (result.confirm) {
      // 这里应该调用支付接口
      await showToast({
        title: '正在跳转支付...',
        icon: 'loading',
      })

      // 模拟支付流程
      setTimeout(() => {
        uni.showToast({
          title: '支付功能开发中',
          icon: 'none',
        })
      }, 1500)
    }
  }
  catch (error) {
    console.error('支付失败:', error)
    uni.showToast({
      title: '支付失败，请重试',
      icon: 'none',
    })
  }
}

// 查看收据
function handleViewReceipt(record) {
  console.log('查看收据:', record)
  uni.showToast({
    title: '查看收据功能开发中',
    icon: 'none',
  })

  // 这里可以跳转到收据详情页面
  // uni.navigateTo({
  //   url: `/pages/receipt/detail?id=${record.id}`
  // })
}

// 加载更多
async function handleLoadMore() {
  if (!hasMore.value)
    return

  try {
    // 模拟加载更多数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里可以调用API加载更多数据
    // const response = await api.getMorePaymentRecords()
    // paymentRecords.value.push(...response.data)

    // 模拟没有更多数据
    hasMore.value = false
  }
  catch (error) {
    console.error('加载更多失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  }
}
</script>

<template>
  <view class="payment-page">
    <!-- 顶部安全区域 -->
    <view class="h-[--safe-top] bg-white flex-none"></view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 待缴费用卡片 -->
      <PendingPaymentCard
        :pending-payment="pendingPayment"
        @pay-now="handlePayNow"
      />

      <!-- 分类标签和记录列表 -->
      <view class="records-section">
        <PaymentCategoryTabs
          :categories="categories"
          :active-category="activeCategory"
          @category-change="handleCategoryChange"
        >
          <template #default>
            <PaymentRecords
              :records="filteredRecords"
              :loading="loading"
              :has-more="hasMore"
              :active-category="activeCategory"
              @load-more="handleLoadMore"
              @view-receipt="handleViewReceipt"
              @pay-now="handlePayNow"
            />
          </template>
        </PaymentCategoryTabs>
      </view>

      <!-- 缴费说明 -->
      <PaymentNotice />
    </view>

    <!-- 底部安全区域 -->
    <view class="h-[--safe-bottom] bg-white flex-none"></view>
  </view>
</template>

<style lang="scss" scoped>
.payment-page {
  min-height: 100vh;
  background: #f8fafc;
  display: flex;
  flex-direction: column;
}

.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.records-section {
  flex: 1;
  background: white;
  border-radius: 16px 16px 0 0;
  margin-top: 8px;
  overflow: hidden;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
}

// 全局样式调整
:deep(.wd-tabs__content) {
  background: #f8fafc;
}

// 响应式设计
@media (max-width: 375px) {
  .records-section {
    margin-top: 4px;
    border-radius: 12px 12px 0 0;
  }
}
</style>
