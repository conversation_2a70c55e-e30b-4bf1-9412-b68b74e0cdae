<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  pendingPayment: {
    type: Object,
    default: () => ({
      amount: 2650.00,
      description: '2024年12月租金 + 物业费',
      dueDate: '2024-12-10',
      isOverdue: false
    })
  }
})

// Emits
const emit = defineEmits(['payNow'])

// 计算属性
const formattedAmount = computed(() => {
  return `¥${props.pendingPayment.amount.toFixed(2)}`
})

const dueDateText = computed(() => {
  const dueDate = new Date(props.pendingPayment.dueDate)
  const today = new Date()
  const diffTime = dueDate - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return `已逾期 ${Math.abs(diffDays)} 天`
  } else if (diffDays === 0) {
    return '今日到期'
  } else if (diffDays <= 3) {
    return `${diffDays} 天后到期`
  } else {
    return `截止日期：${props.pendingPayment.dueDate}`
  }
})

const isUrgent = computed(() => {
  const dueDate = new Date(props.pendingPayment.dueDate)
  const today = new Date()
  const diffTime = dueDate - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 3
})

// 方法
function handlePayNow() {
  uni.vibrateShort?.()
  emit('payNow', props.pendingPayment)
}
</script>

<template>
  <wd-card class="pending-payment-card">
    <template #title>
      <view class="card-header">
        <view class="header-left">
          <view class="i-carbon-wallet text-primary-500 text-xl"></view>
          <view class="header-text">
            <view class="title">待缴费用</view>
            <view class="subtitle">1 笔待缴费用</view>
          </view>
        </view>
        <view class="amount-badge" :class="{ 'urgent': isUrgent }">
          {{ formattedAmount }}
        </view>
      </view>
    </template>

    <view class="card-content">
      <view class="payment-info">
        <view class="info-item">
          <view class="info-label">费用项目</view>
          <view class="info-value">{{ pendingPayment.description }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">到期时间</view>
          <view class="info-value" :class="{ 'text-warning': isUrgent, 'text-error': pendingPayment.isOverdue }">
            {{ dueDateText }}
          </view>
        </view>
      </view>

      <view v-if="isUrgent || pendingPayment.isOverdue" class="warning-tip">
        <view class="i-carbon-warning text-warning mr-1"></view>
        <text class="tip-text">
          {{ pendingPayment.isOverdue ? '逾期将收取滞纳金，请尽快缴纳' : '即将到期，请及时缴纳' }}
        </text>
      </view>
    </view>

    <template #footer>
      <view class="card-footer">
        <wd-button 
          type="primary" 
          size="large" 
          block
          @click="handlePayNow"
          class="pay-button"
        >
          <view class="i-carbon-payment mr-2"></view>
          立即缴纳
        </wd-button>
      </view>
    </template>
  </wd-card>
</template>

<style lang="scss" scoped>
.pending-payment-card {
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  
  :deep(.wd-card__header) {
    padding: 20px 20px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }
  
  :deep(.wd-card__body) {
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }
  
  :deep(.wd-card__footer) {
    padding: 0 20px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-text {
  .title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.2;
  }
  
  .subtitle {
    font-size: 14px;
    color: #6b7280;
    margin-top: 2px;
  }
}

.amount-badge {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 18px;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  
  &.urgent {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  }
}

.card-content {
  .payment-info {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    &:not(:last-child) {
      margin-bottom: 12px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f3f4f6;
    }
  }
  
  .info-label {
    font-size: 14px;
    color: #6b7280;
  }
  
  .info-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 500;
    text-align: right;
    
    &.text-warning {
      color: #f59e0b;
    }
    
    &.text-error {
      color: #ef4444;
    }
  }
  
  .warning-tip {
    display: flex;
    align-items: center;
    background: #fef3c7;
    border: 1px solid #fcd34d;
    border-radius: 8px;
    padding: 10px 12px;
    
    .tip-text {
      font-size: 13px;
      color: #92400e;
      line-height: 1.4;
    }
  }
}

.card-footer {
  .pay-button {
    border-radius: 12px;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(var(--primary-color-500), 0.3);
    
    :deep(.wd-button__text) {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
